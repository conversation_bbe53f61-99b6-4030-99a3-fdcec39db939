﻿using Newtonsoft.Json.Linq;

namespace PBC.HelpdeskService.Models
{


    public class InsertServiceRequestNegativeFeedbackEmailsList
    {
        public string RequestNumber { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public string Customer { get; set; }
        public int Model_Id { get; set; }
        public string ProductType { get; set; }
        public string SerialNumber { get; set; }
        public string ContactPerson { get; set; }
        public string MobileNumber { get; set; }
        public string CallDescription { get; set; }
        public string CustomerRating { get; set; }
        public string Feedback { get; set; }
    }
    public class SelectSRDetailsList
    {
        public int Company_ID { get; set; }
        public int ServiceRequestID { get; set; }
        public int ChildTicket_Sequence_ID { get; set; }
        public int Branch { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string UserCulture { get; set; }
        public string FromOEMDasboard { get; set; }
        public string ROPN { get; set; }
        public string Reopen { get; set; }
    }

    public partial class GNM_Company
    {
        public GNM_Company()
        {
            this.GNM_Company_Company_Relation = new HashSet<GNM_Company_Company_Relation>();
            this.GNM_Company_Company_Relation1 = new HashSet<GNM_Company_Company_Relation>();
            this.GNM_CompanyBrands = new HashSet<GNM_CompanyBrands>();
            this.GNM_CompanyLocale = new HashSet<GNM_CompanyLocale>();
            this.GNM_Branch = new HashSet<GNM_Branch>();
            this.GNM_CompanyFinancialYear = new HashSet<GNM_CompanyFinancialYear>();
            this.GNM_HEADERFOOTERPRINT = new HashSet<GNM_HEADERFOOTERPRINT>();
            this.GNM_TERMSANDCONDITIONS = new HashSet<GNM_TERMSANDCONDITIONS>();
            this.GNM_HourlyRate = new HashSet<GNM_HourlyRate>();
            this.GNM_CompanyEmployee = new HashSet<GNM_CompanyEmployee>();
        }

        public int Company_ID { get; set; }
        public string Company_Name { get; set; }
        public string Company_ShortName { get; set; }
        public int Currency_ID { get; set; }
        public string Company_Address { get; set; }
        public string Company_Type { get; set; }
        public bool Company_Active { get; set; }
        public string Company_LogoName { get; set; }
        public Nullable<int> Company_Parent_ID { get; set; }
        public string Remarks { get; set; }
        public byte DefaultGridSize { get; set; }
        public Nullable<decimal> JobCardCushionHours { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<int> CompanyTheme_ID { get; set; }
        public Nullable<int> QuotationValidity { get; set; }
        public string CompanyFont { get; set; }
        public Nullable<decimal> InventoryCarryingFactoy_Percentage { get; set; }
        public Nullable<int> OrderingCost { get; set; }

        public virtual ICollection<GNM_Company_Company_Relation> GNM_Company_Company_Relation { get; set; }
        public virtual ICollection<GNM_Company_Company_Relation> GNM_Company_Company_Relation1 { get; set; }
        public virtual ICollection<GNM_CompanyBrands> GNM_CompanyBrands { get; set; }
        public virtual ICollection<GNM_CompanyLocale> GNM_CompanyLocale { get; set; }
        public virtual ICollection<GNM_Branch> GNM_Branch { get; set; }
        public virtual ICollection<GNM_CompanyFinancialYear> GNM_CompanyFinancialYear { get; set; }
        public virtual ICollection<GNM_HEADERFOOTERPRINT> GNM_HEADERFOOTERPRINT { get; set; }
        public virtual ICollection<GNM_TERMSANDCONDITIONS> GNM_TERMSANDCONDITIONS { get; set; }
        public virtual ICollection<GNM_HourlyRate> GNM_HourlyRate { get; set; }
        public virtual ICollection<GNM_CompanyEmployee> GNM_CompanyEmployee { get; set; }
    }
    public class CaseProgressObjects
    {
        public int RoleID { get; set; }

        public int CompanyID { get; set; }

        public int transactionNumber { get; set; }

        public int workFlowID { get; set; }

        public int currentStepID { get; set; }

        public DateTime receivedTime { get; set; }

        public int actionID { get; set; }

        public int actionBy { get; set; }

        public int AssignTo { get; set; }

        public string actionRemarks { get; set; }

        public byte addresseType { get; set; }

        public DateTime actionTime { get; set; }

        public string smsTextAddressee { get; set; }

        public string smsTextCustomer { get; set; }

        public string customerMobileNumber { get; set; }

        public string customerEmailID { get; set; }

        public string emailSubAddressee { get; set; }

        public string emailBodyAddress { get; set; }

        public string emailBodyCustomer { get; set; }

        public string emailSubCustomer { get; set; }

        public int NextStepID { get; set; }

        public string CustomerBcc { get; set; }

        public string customerCC { get; set; }

        public string AddresseBcc { get; set; }

        public string AddresseCC { get; set; }
    }

    public partial class GNM_ModelLocale
    {
        public int ModelLocale_ID { get; set; }
        public int Model_ID { get; set; }
        public string Model_Name { get; set; }
        public int Language_ID { get; set; }
        public string Model_Description { get; set; }

        public virtual GNM_Model GNM_Model { get; set; }
    }
    public partial class GNM_ModelPriceDetails
    {
        public int ModelPriceDetails_ID { get; set; }
        public int Model_ID { get; set; }
        public decimal ListPrice { get; set; }
        public System.DateTime EffectiveFrom { get; set; }
        public int Company_ID { get; set; }

        public virtual GNM_Model GNM_Model { get; set; }
    }

    public partial class GNM_ModelQuickListDetails
    {
        public GNM_ModelQuickListDetails()
        {
            this.GNM_ModelQuickListDetailsLocale = new HashSet<GNM_ModelQuickListDetailsLocale>();
        }

        public int ModelQuickChecklist_ID { get; set; }
        public int Model_ID { get; set; }
        public string Description { get; set; }
        public bool IsMandatory { get; set; }
        public bool IsPhotoRequired { get; set; }
        public Nullable<byte> IsDefaultCheck { get; set; }
        public string Minvalue { get; set; }
        public string Maxvalue { get; set; }

        public virtual ICollection<GNM_ModelQuickListDetailsLocale> GNM_ModelQuickListDetailsLocale { get; set; }
        public virtual GNM_Model GNM_Model { get; set; }
    }
    public partial class GNM_ModelQuickListDetailsLocale
    {
        public int ModelQuickChecklistLocale_ID { get; set; }
        public int ModelQuickChecklist_ID { get; set; }
        public string Description { get; set; }
        public Nullable<int> Language_ID { get; set; }

        public virtual GNM_ModelQuickListDetails GNM_ModelQuickListDetails { get; set; }
    }
    public class WF_Sms
    {
        public int Sms_ID { get; set; }

        public string Sms_Text { get; set; }

        public string Sms_Mobile_Number { get; set; }

        public DateTime? Sms_Queue_Date { get; set; }

        public DateTime? Sms_Sent_Date { get; set; }

        public bool Sms_SentStatus { get; set; }

        public int Template_ID { get; set; }

        public string Parameter1_value { get; set; }

        public string Parameter2_value { get; set; }

        public string Parameter3_value { get; set; }

        public string Parameter4_value { get; set; }
    }
    public partial class GNM_ATTACHMENTDETAIL
    {
        public int ATTACHMENTDETAIL_ID { get; set; }
        public string FILENAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public int UPLOADBY { get; set; }
        public System.DateTime UPLOADDATE { get; set; }
        public string REMARKS { get; set; }
        public int COMPANY_ID { get; set; }
        public int OBJECT_ID { get; set; }
        public int TRANSACTION_ID { get; set; }
        public Nullable<int> DETAIL_ID { get; set; }
        public string TABLE_NAME { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }
    public partial class GNM_GPSLOG
    {
        public int GPSLOG_ID { get; set; }
        public int OBJECT_ID { get; set; }
        public int RECORD_ID { get; set; }
        public double LATITUDE { get; set; }
        public double LONGITUDE { get; set; }
        public System.DateTime MODIFIEDDATE { get; set; }
        public int USER_ID { get; set; }
        public int COMPANY_ID { get; set; }
        public int BRANCH_ID { get; set; }
        public string ActionName { get; set; }
        public Nullable<bool> IsFromMobile { get; set; }
        public Nullable<System.DateTime> LOGINDATETIME { get; set; }
        public Nullable<System.DateTime> LOGOUTDATETIME { get; set; }
        public Nullable<int> Menu_ID { get; set; }
        public string HostName { get; set; }
        public string AddressFamily { get; set; }
        public string isTrustedHost { get; set; }
        public string AddressList { get; set; }
        public string LocalIPAddress { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }

    public partial class GNM_MODELATTACHMENTDETAIL
    {
        public int MODELATTACHMENTDETAIL_ID { get; set; }
        public int MODEL_ID { get; set; }
        public string FILENAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public int UPLOADBY { get; set; }
        public System.DateTime UPLOADDATE { get; set; }
        public int OBJECT_ID { get; set; }
        public bool AUTODISPLAY { get; set; }
        public bool PRINT { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }

    public partial class GNM_REF_ATTACHMENTDETAIL
    {
        public int REFATTACHMENTDETAIL_ID { get; set; }
        public string FILENAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public int UPLOADBY { get; set; }
        public System.DateTime UPLOADDATE { get; set; }
        public string REMARKS { get; set; }
        public int COMPANY_ID { get; set; }
        public int OBJECT_ID { get; set; }
        public int TRANSACTION_ID { get; set; }
        public Nullable<int> DETAIL_ID { get; set; }
        public string TABLE_NAME { get; set; }
        public Nullable<bool> ISACTIVE { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }

    public partial class GNM_UserCompany
    {
        public int UserCompanyDet_ID { get; set; }
        public int User_ID { get; set; }
        public int Company_ID { get; set; }

        public virtual GNM_User GNM_User { get; set; }
    }
    public partial class GNM_UserLocale
    {
        public int User_Locale_ID { get; set; }
        public int User_ID { get; set; }
        public int Language_ID { get; set; }
        public string User_Name { get; set; }

        public virtual GNM_User GNM_User { get; set; }
    }
    public class getProductUniqueNumberFList
    {
        public int Company_ID { get; set; }
        public int ModelID { get; set; }
        public string SerialNumber { get; set; }

    }
    public partial class GNM_UserRole
    {
        public int UserRole_ID { get; set; }
        public int User_ID { get; set; }
        public int Role_ID { get; set; }

        public virtual GNM_Role GNM_Role { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }
    public class getProductWarrantyList
    {
        public int Company_ID { get; set; }
        public int ModelID { get; set; }
        public int Branch { get; set; }
        public string SerialNumber { get; set; }

    }
    public class GetAllProductDetailsForDealerList
    {
        public int Company_ID { get; set; }
        public int Language_ID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
    }
    public class ContactPersonMasterSaveList
    {
        public int Language_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string Data { get; set; }
    }
    public class getCustomerDetailsByEmailCList
    {
        public int Company_ID { get; set; }
        public int Language_ID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public int Branch { get; set; }
        public string email { get; set; }
    }
    public class getCustomerDetailsByPhoneList
    {
        public int Company_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int Branch { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string phoneNo { get; set; }
    }
    public class getBrandProductTypecList
    {
        public int UserLanguageID { get; set; }
        public int ModelID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
    }

    public class ValidateSerialNumberList
    {
        public string SerialNumber { get; set; }
    }
    public class ValidateReadingList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int ModelID { get; set; }
        public int Reading { get; set; }
        public string SerialNumber { get; set; }
    }
    public class loadIssueSubAreaList
    {
        public int IssueAreaID { get; set; }
        public int EnquiryType_ID { get; set; }
        public int Language_ID { get; set; }
        public int Company_ID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
    }
    public class loadMastersList
    {
        public int Language_ID { get; set; }
        public int Company_ID { get; set; }
        public int isComp { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string MasterName { get; set; }
    }
    public class SelectPartyDetailGridEList
    {
        public int Company_ID { get; set; }
        public int UserLanguageID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string UserCulture { get; set; }
        public string partyName { get; set; }
    }
    public class GetPartyDetailsbyIDEList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int PartyID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string UserCulture { get; set; }
    }
    public class PartyContactPerson
    {
        public int PartyContactPerson_ID { get; set; }
        public string PartyContactPerson_IsActive { get; set; }
        public string Party_IsDefaultContact { get; set; }
        public bool IsDefault { get; set; }
        public string PartyContactPerson_Name { get; set; }
        public string PartyContactPerson_DOB { get; set; }
        public string PartyContactPerson_Department { get; set; }
        public string PartyContactPerson_Email { get; set; }
        public string PartyContactPerson_Phone { get; set; }
        public string PartyContactPerson_Mobile { get; set; }
        public string PartyContactPerson_Remarks { get; set; }
        public string Local { get; set; }
    }
    public class GetPartyDetailsList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int UserLanguageID { get; set; }
        public int Party_ID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string partyName { get; set; }
    }
    public class GetRolesForActionsList
    {
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int WFCurrentStepID { get; set; }
        public int ActionID { get; set; }
        public int TransactionID { get; set; }
    }
    public partial class GNM_WALKAROUNDATTACHMENTDETAIL
    {
        public int ATTACHMENTDETAIL_ID { get; set; }
        public string FILENAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public int UPLOADBY { get; set; }
        public System.DateTime UPLOADDATE { get; set; }
        public string REMARKS { get; set; }
        public int COMPANY_ID { get; set; }
        public int OBJECT_ID { get; set; }
        public int TRANSACTION_ID { get; set; }
        public Nullable<int> DETAIL_ID { get; set; }
        public string TABLE_NAME { get; set; }
        public string TYPE { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }


    public partial class GNM_RoleObject
    {
        public int RoleObject_ID { get; set; }
        public int Role_ID { get; set; }
        public int Object_ID { get; set; }
        public bool RoleObject_Create { get; set; }
        public bool RoleObject_Read { get; set; }
        public bool RoleObject_Update { get; set; }
        public bool RoleObject_Delete { get; set; }
        public bool RoleObject_Print { get; set; }
        public bool RoleObject_Export { get; set; }
        public bool RoleObject_Import { get; set; }

        public virtual GNM_Role GNM_Role { get; set; }
        public virtual GNM_Object GNM_Object { get; set; }
    }
    public partial class GNM_CompanyCalendarNonWorkingRule
    {
        public int CompanyCalendarNonWorkingDay_ID { get; set; }
        public int CompanyCalender_ID { get; set; }
        public byte WeekDay { get; set; }
        public Nullable<int> RegularHoursFrom { get; set; }
        public Nullable<int> RegularHoursTo { get; set; }
        public Nullable<int> OvertimeHoursFrom { get; set; }
        public Nullable<int> OvertimeHoursTo { get; set; }
        public Nullable<int> DoubletimeHoursFrom { get; set; }
        public Nullable<int> DoubletimeHoursTo { get; set; }
        public Nullable<System.DateTime> WeekDate { get; set; }
        public string WeekNumber { get; set; }
        public Nullable<bool> IsBetweenWorkingDays { get; set; }

        public virtual GNM_CompanyCalender GNM_CompanyCalender { get; set; }
    }
    public class DateDetails
    {
        public CallDateDetail Details { get; set; }
        public int Year { get; set; }
    }
    public partial class GNM_CompanyCalender
    {
        public GNM_CompanyCalender()
        {
            this.GNM_CompanyCalendarNonWorkingRule = new HashSet<GNM_CompanyCalendarNonWorkingRule>();
            this.GNM_CompanyCalendarWorkingDate = new HashSet<GNM_CompanyCalendarWorkingDate>();
            this.GNM_CompanyCalenderHolidays = new HashSet<GNM_CompanyCalenderHolidays>();
        }

        public int CompanyCalender_ID { get; set; }
        public int Company_ID { get; set; }
        public int CompanyCalender_Year { get; set; }
        public int Shift_ID { get; set; }
        public System.TimeSpan CompanyCalender_StartTime { get; set; }
        public System.TimeSpan CompanyCalender_EndTime { get; set; }
        public string CompanyCalender_WorkingDays { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public bool IsGeneralShift { get; set; }
        public System.TimeSpan Break_StartTime { get; set; }
        public System.TimeSpan Break_EndTime { get; set; }
        public Nullable<int> Branch_ID { get; set; }
        public Nullable<System.TimeSpan> ShiftHours { get; set; }
        public string DoubleTimeApplicableDays { get; set; }
        public Nullable<int> OverTimeMinutes { get; set; }
        public Nullable<int> DoubleTimeMinutes { get; set; }
        public Nullable<int> ShiftType_ID { get; set; }
        public Nullable<int> ShiftDays { get; set; }
        public Nullable<bool> CompanyCalendarActive { get; set; }
        public bool IsByDate { get; set; }

        public virtual ICollection<GNM_CompanyCalendarNonWorkingRule> GNM_CompanyCalendarNonWorkingRule { get; set; }
        public virtual ICollection<GNM_CompanyCalendarWorkingDate> GNM_CompanyCalendarWorkingDate { get; set; }
        public virtual ICollection<GNM_CompanyCalenderHolidays> GNM_CompanyCalenderHolidays { get; set; }
    }
    public partial class GNM_CompanyCalendarWorkingDate
    {
        public int CompanyCalendarWorkingDate_ID { get; set; }
        public int CompanyCalender_ID { get; set; }
        public System.DateTime WeekDate { get; set; }
        public string WeekNumber { get; set; }

        public virtual GNM_CompanyCalender GNM_CompanyCalender { get; set; }
    }
    public partial class GNM_CompanyCalenderHolidays
    {
        public int CompanyCalenderHoliday_ID { get; set; }
        public int CompanyCalender_ID { get; set; }
        public System.DateTime CompanyCalenderHoliday_Date { get; set; }
        public string CompanyCalenderHoliday_Name { get; set; }
        public bool IsDoubleTimeApplicable { get; set; }

        public virtual GNM_CompanyCalender GNM_CompanyCalender { get; set; }
    }
    public class CallDateDetail
    {
        public List<string> WorkDays { get; set; }
        public TimeSpan startTime { get; set; }
        public TimeSpan endTime { get; set; }
        public TimeSpan BreakstartTime { get; set; }
        public TimeSpan BreakEndTime { get; set; }
        public double startTimeMinutes { get; set; }
        public double endTimeMinutes { get; set; }
        public double BreakstartTimeMinutes { get; set; }
        public double BreakEndTimeMinute { get; set; }
        public double WorkHours { get; set; }
    }
    public partial class GNM_StateLocale
    {
        public int StateLocale_ID { get; set; }
        public int State_ID { get; set; }
        public string State_Name { get; set; }
        public int Language_ID { get; set; }

        public virtual GNM_State GNM_State { get; set; }
    }
    public partial class GNM_State
    {
        public GNM_State()
        {
            this.GNM_StateLocale = new HashSet<GNM_StateLocale>();
        }

        public int State_ID { get; set; }
        public int Country_ID { get; set; }
        public string State_Name { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public bool State_IsActive { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public string StateCode { get; set; }
        public string Stcd { get; set; }

        public virtual ICollection<GNM_StateLocale> GNM_StateLocale { get; set; }
    }
    public partial class GNM_Role
    {
        public GNM_Role()
        {
            this.GNM_RoleObject = new HashSet<GNM_RoleObject>();
            this.GNM_UserRole = new HashSet<GNM_UserRole>();
        }

        public int Role_ID { get; set; }
        public int Company_ID { get; set; }
        public string Role_Name { get; set; }

        public virtual ICollection<GNM_RoleObject> GNM_RoleObject { get; set; }
        public virtual ICollection<GNM_UserRole> GNM_UserRole { get; set; }
    }
    public partial class GNM_PrefixSuffix
    {
        public int PrefixSuffix_ID { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> Branch_ID { get; set; }
        public int Object_ID { get; set; }
        public int Start_Number { get; set; }
        public string Prefix { get; set; }
        public string Suffix { get; set; }
        public System.DateTime FromDate { get; set; }
        public System.DateTime ToDate { get; set; }
        public int ModifiedBY { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public Nullable<int> Company_FinancialYear_ID { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
    }
    public partial class GNM_Object
    {
        public GNM_Object()
        {
            this.GNM_RoleObject = new HashSet<GNM_RoleObject>();
            this.GNM_PrefixSuffix = new HashSet<GNM_PrefixSuffix>();
            this.GNM_GPSLOG = new HashSet<GNM_GPSLOG>();
            this.GNM_ATTACHMENTDETAIL = new HashSet<GNM_ATTACHMENTDETAIL>();
            this.GNM_MODELATTACHMENTDETAIL = new HashSet<GNM_MODELATTACHMENTDETAIL>();
            this.GNM_REF_ATTACHMENTDETAIL = new HashSet<GNM_REF_ATTACHMENTDETAIL>();
            this.GNM_WALKAROUNDATTACHMENTDETAIL = new HashSet<GNM_WALKAROUNDATTACHMENTDETAIL>();
            this.GNM_DashboardConfiguration = new HashSet<GNM_DashboardConfiguration>();
        }

        public int Object_ID { get; set; }
        public string Object_Name { get; set; }
        public string Read_Action { get; set; }
        public string Create_Action { get; set; }
        public string Update_Action { get; set; }
        public string Delete_Action { get; set; }
        public string Export_Action { get; set; }
        public string Print_Action { get; set; }
        public bool Object_IsActive { get; set; }
        public string Object_Description { get; set; }
        public string Import_Action { get; set; }
        public string Object_Type { get; set; }

        public virtual ICollection<GNM_RoleObject> GNM_RoleObject { get; set; }
        public virtual ICollection<GNM_PrefixSuffix> GNM_PrefixSuffix { get; set; }
        public virtual ICollection<GNM_GPSLOG> GNM_GPSLOG { get; set; }
        public virtual ICollection<GNM_ATTACHMENTDETAIL> GNM_ATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_MODELATTACHMENTDETAIL> GNM_MODELATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_REF_ATTACHMENTDETAIL> GNM_REF_ATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_WALKAROUNDATTACHMENTDETAIL> GNM_WALKAROUNDATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_DashboardConfiguration> GNM_DashboardConfiguration { get; set; }
    }

    public partial class GNM_TAMSAdjustmentLog
    {
        public int TAMSAdjustmentLog_ID { get; set; }
        public int Employee_ID { get; set; }
        public int ActivityType { get; set; }
        public System.DateTime Date { get; set; }
        public Nullable<System.DateTime> ActualStartDatetime { get; set; }
        public Nullable<System.DateTime> ActualEndDatetime { get; set; }
        public System.DateTime AdjustedStartDatetime { get; set; }
        public System.DateTime AdjustedEndDatetime { get; set; }
        public int AdjustedBy_ID { get; set; }
        public System.DateTime AdjustedDate { get; set; }
        public string LaborCode { get; set; }

        public virtual GNM_User GNM_User { get; set; }
    }
    public partial class GNM_RegilarizeWindowCloseLog
    {
        public int WindowCloseLog_ID { get; set; }
        public System.DateTime WindowClosDateTime { get; set; }
        public int USER_ID { get; set; }
        public int Employee_ID { get; set; }
        public int BRANCH_ID { get; set; }
        public Nullable<System.DateTime> FromDateTime { get; set; }
        public Nullable<System.DateTime> ToDateTime { get; set; }
        public int Shift_ID { get; set; }

        public virtual GNM_User GNM_User { get; set; }
    }
    public partial class GNM_DashboardConfiguration
    {
        public int DashboardConfiguration_ID { get; set; }
        public Nullable<int> User_ID { get; set; }
        public Nullable<int> Object_ID { get; set; }
        public Nullable<bool> IsVisible { get; set; }
        public Nullable<bool> IsExpand { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }

    public class WF_WFStepStatusLocale
    {
        public int WFStepStatusLocale_ID { get; set; }

        public int WFStepStatus_ID { get; set; }

        public int Language_ID { get; set; }

        public string WFStepStatus_Nm { get; set; }

        public string StepStatusCode { get; set; }

        public virtual WF_WFStepStatus GNM_WFStepStatus { get; set; }
    }
    public partial class GNM_PartyContactLocale
    {
        public int PartyContactLocale_ID { get; set; }
        public int PartyContactPerson_ID { get; set; }
        public string PartyContact_Name { get; set; }
        public int Language_ID { get; set; }
        public string PartyContact_Department { get; set; }
        public string PartyContact_Remarks { get; set; }
        public Nullable<int> Party_ID { get; set; }

        public virtual GNM_PartyContactPersonDetails GNM_PartyContactPersonDetails { get; set; }
    }
    public partial class GNM_User
    {
        public GNM_User()
        {
            this.GNM_ATTACHMENTDETAIL = new HashSet<GNM_ATTACHMENTDETAIL>();
            this.GNM_GPSLOG = new HashSet<GNM_GPSLOG>();
            this.GNM_MODELATTACHMENTDETAIL = new HashSet<GNM_MODELATTACHMENTDETAIL>();
            this.GNM_REF_ATTACHMENTDETAIL = new HashSet<GNM_REF_ATTACHMENTDETAIL>();
            this.GNM_UserCompany = new HashSet<GNM_UserCompany>();
            this.GNM_UserLocale = new HashSet<GNM_UserLocale>();
            this.GNM_UserRole = new HashSet<GNM_UserRole>();
            this.GNM_WALKAROUNDATTACHMENTDETAIL = new HashSet<GNM_WALKAROUNDATTACHMENTDETAIL>();
            this.GNM_TAMSAdjustmentLog = new HashSet<GNM_TAMSAdjustmentLog>();
            this.GNM_RegilarizeWindowCloseLog = new HashSet<GNM_RegilarizeWindowCloseLog>();
            this.GNM_DashboardConfiguration = new HashSet<GNM_DashboardConfiguration>();
        }

        public int User_ID { get; set; }
        public string User_Name { get; set; }
        public string User_LoginID { get; set; }
        public string User_Password { get; set; }
        public bool User_IsActive { get; set; }
        public bool User_Locked { get; set; }
        public Nullable<int> User_LoginCount { get; set; }
        public Nullable<int> User_FailedCount { get; set; }
        public int Company_ID { get; set; }
        public int Language_ID { get; set; }
        public byte User_Type_ID { get; set; }
        public Nullable<int> Employee_ID { get; set; }
        public Nullable<int> Partner_ID { get; set; }
        public string LandingPage { get; set; }
        public string User_IPAddress { get; set; }
        public Nullable<int> WareHouse_ID { get; set; }
        public string ReleaseVersionPopup { get; set; }

        public virtual ICollection<GNM_ATTACHMENTDETAIL> GNM_ATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_GPSLOG> GNM_GPSLOG { get; set; }
        public virtual ICollection<GNM_MODELATTACHMENTDETAIL> GNM_MODELATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_REF_ATTACHMENTDETAIL> GNM_REF_ATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_UserCompany> GNM_UserCompany { get; set; }
        public virtual ICollection<GNM_UserLocale> GNM_UserLocale { get; set; }
        public virtual ICollection<GNM_UserRole> GNM_UserRole { get; set; }
        public virtual ICollection<GNM_WALKAROUNDATTACHMENTDETAIL> GNM_WALKAROUNDATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_TAMSAdjustmentLog> GNM_TAMSAdjustmentLog { get; set; }
        public virtual ICollection<GNM_RegilarizeWindowCloseLog> GNM_RegilarizeWindowCloseLog { get; set; }
        public virtual ICollection<GNM_DashboardConfiguration> GNM_DashboardConfiguration { get; set; }
    }
    public class GetActionsList
    {
        public int WFCurrentStepID { get; set; }
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int Branch { get; set; }
    }
    public class GetProductDetailsFList
    {
        public int Company_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int PartyID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
    }
    public partial class GNM_ProductWarranty
    {
        public GNM_ProductWarranty()
        {
            this.GNM_ProductWarrantyLocale = new HashSet<GNM_ProductWarrantyLocale>();
        }

        public int ProductWarranty_ID { get; set; }
        public int Product_ID { get; set; }
        public string ProductWarranty_Description { get; set; }
        public System.DateTime ProductWarranty_IssueDate { get; set; }
        public Nullable<System.DateTime> ProductWarranty_FromDate { get; set; }
        public Nullable<System.DateTime> ProductWarranty_ToDate { get; set; }
        public Nullable<int> ProductWarranty_ReadingLimit { get; set; }
        public Nullable<int> WarrantyType_ID { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public Nullable<int> ServiceType_ID { get; set; }
        public bool ScheduleType { get; set; }
        public Nullable<System.DateTime> RecurringStartDate { get; set; }
        public Nullable<int> RepeatEveryDays { get; set; }
        public Nullable<bool> IsAutometicWarranty { get; set; }
        public Nullable<int> WarrantyIdentifier_ID { get; set; }
        public Nullable<int> OdometerLimitInMiles { get; set; }
        public Nullable<int> OdometerLimitInKM { get; set; }

        public virtual ICollection<GNM_ProductWarrantyLocale> GNM_ProductWarrantyLocale { get; set; }
        public virtual GNM_Product GNM_Product { get; set; }
    }
    public class APINameID
    {
        public int ID { get; set; }
        public string Name { get; set; }
    }
    public partial class GNM_ProductWarrantyLocale
    {
        public int ProductWarrantyLocale_ID { get; set; }
        public int ProductWarranty_ID { get; set; }
        public int Language_ID { get; set; }
        public string Description { get; set; }

        public virtual GNM_ProductWarranty GNM_ProductWarranty { get; set; }
    }
    public class CreditCheckResult
    {
        public decimal? TotalServiceCreditLimit;
        public decimal? TotalServiceOutStanding;
        public bool CreditLimit;
        public bool OtherOpenCreditLimit;
        public bool IsCreditLimitSet;
        public decimal? PartyServiceCreditLimit;
        public decimal? PartyServiceOutStandingCredit;
        public decimal? PartyServiceCreditLimitinUSD;
        public decimal? PartyServiceOutStandingCreditinUSD;
        public decimal? TotalValue;
    }
    public class PartyContactPersonLocale
    {
        public int PartyContactPerson_ID
        {
            get;
            set;
        }
        public String PartyContactPerson_Name
        {
            get;
            set;
        }
        public bool RefMasterDetail_IsDefault { get; set; }
        public String PartyContactPerson_Email
        {
            get;
            set;
        }
        public String PartyContactPerson_Mobile
        {
            get;
            set;
        }
    }
    public partial class GNM_ModelSalesPriceDetails
    {
        public int ModelSalesPriceDetails_ID { get; set; }
        public int Model_ID { get; set; }
        public decimal ListPrice { get; set; }
        public decimal DealerNetPrice { get; set; }
        public decimal MRP { get; set; }
        public System.DateTime EffectiveFrom { get; set; }

        public virtual GNM_Model GNM_Model { get; set; }
    }
    public partial class GNM_ModelServiceChargeDetail
    {
        public int ModelServiceChargeDetail_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public Nullable<int> ServiceType_ID { get; set; }
        public Nullable<decimal> ServiceCharge { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public Nullable<System.DateTime> EffectiveDate { get; set; }

        public virtual GNM_Model GNM_Model { get; set; }
    }
    public partial class GNM_MODELSERVICETYPEDET
    {
        public int MODELSERVICETYPEDET_ID { get; set; }
        public int MODEL_ID { get; set; }
        public int SERVICETYPE_ID { get; set; }
        public int COMPANY_ID { get; set; }

        public virtual GNM_Model GNM_Model { get; set; }
    }
    public partial class GNM_ModelWarrantyDefinitionDetails
    {
        public int ModelWarrantyDefinitionDetails_ID { get; set; }
        public int Model_ID { get; set; }
        public Nullable<int> WarrantyType_ID { get; set; }
        public Nullable<int> ServiceType_ID { get; set; }
        public Nullable<bool> Applicable_Type { get; set; }
        public int WarrantyMonths { get; set; }
        public int WarrantyHours { get; set; }
        public System.DateTime EffectiveFrom { get; set; }
        public int Company_ID { get; set; }

        public virtual GNM_Model GNM_Model { get; set; }
    }
    public partial class GNM_ModelRedCarpetListDetailsLocale
    {
        public int ModelRedCarpetChecklistLocale_ID { get; set; }
        public int ModelRedCarpetChecklist_ID { get; set; }
        public string Description { get; set; }
        public Nullable<int> Language_ID { get; set; }

        public virtual GNM_ModelRedCarpetListDetails GNM_ModelRedCarpetListDetails { get; set; }
    }
    public partial class GNM_Model
    {
        public GNM_Model()
        {
            this.GNM_ModelLocale = new HashSet<GNM_ModelLocale>();
            this.GNM_ModelPriceDetails = new HashSet<GNM_ModelPriceDetails>();
            this.GNM_ModelQuickListDetails = new HashSet<GNM_ModelQuickListDetails>();
            this.GNM_ModelRedCarpetListDetails = new HashSet<GNM_ModelRedCarpetListDetails>();
            this.GNM_ModelSalesPriceDetails = new HashSet<GNM_ModelSalesPriceDetails>();
            this.GNM_ModelServiceChargeDetail = new HashSet<GNM_ModelServiceChargeDetail>();
            this.GNM_MODELSERVICETYPEDET = new HashSet<GNM_MODELSERVICETYPEDET>();
            this.GNM_ModelWarrantyDefinitionDetails = new HashSet<GNM_ModelWarrantyDefinitionDetails>();
        }

        public int Model_ID { get; set; }
        public int ProductType_ID { get; set; }
        public int Brand_ID { get; set; }
        public string Model_Name { get; set; }
        public bool Model_IsActive { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<int> ServiceType_ID { get; set; }
        public Nullable<int> ServiceFrequency { get; set; }
        public string Model_Description { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public string Series { get; set; }

        public virtual ICollection<GNM_ModelLocale> GNM_ModelLocale { get; set; }
        public virtual ICollection<GNM_ModelPriceDetails> GNM_ModelPriceDetails { get; set; }
        public virtual ICollection<GNM_ModelQuickListDetails> GNM_ModelQuickListDetails { get; set; }
        public virtual ICollection<GNM_ModelRedCarpetListDetails> GNM_ModelRedCarpetListDetails { get; set; }
        public virtual ICollection<GNM_ModelSalesPriceDetails> GNM_ModelSalesPriceDetails { get; set; }
        public virtual ICollection<GNM_ModelServiceChargeDetail> GNM_ModelServiceChargeDetail { get; set; }
        public virtual ICollection<GNM_MODELSERVICETYPEDET> GNM_MODELSERVICETYPEDET { get; set; }
        public virtual ICollection<GNM_ModelWarrantyDefinitionDetails> GNM_ModelWarrantyDefinitionDetails { get; set; }
    }

    public partial class HD_IssueSubAreaLocale
    {
        public int IssueSubAreaLocale_ID { get; set; }
        public int IssueSubArea_ID { get; set; }
        public string IssueSubArea_ShortName { get; set; }
        public string IssueSubArea_Description { get; set; }
        public int Language_ID { get; set; }
        public bool IssueSubArea_IsActive { get; set; }
        public Nullable<int> EnquiryType_ID { get; set; }

        public virtual HD_IssueSubArea HD_IssueSubArea { get; set; }
    }
    public partial class HD_IssueSubArea
    {
        public HD_IssueSubArea()
        {
            this.HD_IssueSubAreaLocale = new HashSet<HD_IssueSubAreaLocale>();
        }

        public int IssueSubArea_ID { get; set; }
        public int IssueArea_ID { get; set; }
        public string IssueSubArea_ShortName { get; set; }
        public string IssueSubArea_Description { get; set; }
        public bool IssueSubArea_IsActive { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> EnquiryType_ID { get; set; }

        public virtual ICollection<HD_IssueSubAreaLocale> HD_IssueSubAreaLocale { get; set; }
    }
    public partial class GNM_ProductTypeLocale
    {
        public int ProductTypeLocale_ID { get; set; }
        public int ProductType_ID { get; set; }
        public string ProductType_Name { get; set; }
        public int Language_ID { get; set; }

        public virtual GNM_ProductType GNM_ProductType { get; set; }
    }
    public class WF_Email
    {
        public int Email_ID { get; set; }

        public string Email_Subject { get; set; }

        public string Email_Body { get; set; }

        public string Email_To { get; set; }

        public string Email_cc { get; set; }

        public string Email_Bcc { get; set; }

        public DateTime? Email_Queue_Date { get; set; }

        public DateTime? Email_Sent_Date { get; set; }

        public bool Email_SentStatus { get; set; }

        public string Email_Attachments { get; set; }

        public bool? Email_IsError { get; set; }

        public byte? NoOfAttempts { get; set; }
    }
    public partial class GNM_ProductType
    {
        public GNM_ProductType()
        {
            this.GNM_ProductTypeLocale = new HashSet<GNM_ProductTypeLocale>();
        }

        public int ProductType_ID { get; set; }
        public int Brand_ID { get; set; }
        public string ProductType_Name { get; set; }
        public bool ProductType_IsActive { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }

        public virtual ICollection<GNM_ProductTypeLocale> GNM_ProductTypeLocale { get; set; }
    }

    public class HelpDeskSavePartsList
    {
        public string data { get; set; }
        public int SRIDGlobal { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
        public JObject jObj { get; set; }
    }

    // Added missing UnblockParts model for parts blocking/unblocking functionality
    public class UnblockParts
    {
        public int STOCKBLOCKINGPARTSDETAIL_ID { get; set; }
        public int COMPANY_ID { get; set; }
        public int BRANCH_ID { get; set; }
        public int WAREHOUSE_ID { get; set; }
        public decimal BLOCKEDQUANTITY { get; set; }
    }
    public partial class GNM_ModelRedCarpetListDetails
    {
        public GNM_ModelRedCarpetListDetails()
        {
            this.GNM_ModelRedCarpetListDetailsLocale = new HashSet<GNM_ModelRedCarpetListDetailsLocale>();
        }

        public int ModelRedCarpetChecklist_ID { get; set; }
        public int Model_ID { get; set; }
        public string Description { get; set; }
        public bool IsMandatory { get; set; }
        public bool IsPhotoRequired { get; set; }

        public virtual ICollection<GNM_ModelRedCarpetListDetailsLocale> GNM_ModelRedCarpetListDetailsLocale { get; set; }
        public virtual GNM_Model GNM_Model { get; set; }
    }
    public partial class GNM_BranchLocale
    {
        public int Branch_Locale_ID { get; set; }
        public int Branch_ID { get; set; }
        public int Language_ID { get; set; }
        public string Branch_Name { get; set; }
        public string Branch_Address { get; set; }
        public string Branch_Location { get; set; }
        public string Branch_ShortName { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
    }
    public class SaveProductDetailsList
    {
        public JObject jObj { get; set; }
        public int SRIDGlobal { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
    }
    public partial class GNM_CompanyEmployee
    {
        public GNM_CompanyEmployee()
        {
            this.GNM_EmployeeTrainingDetails = new HashSet<GNM_EmployeeTrainingDetails>();
            this.GNM_CompanyEmployeeLocale = new HashSet<GNM_CompanyEmployeeLocale>();
            this.GNM_CompanyEmployeeSkillset = new HashSet<GNM_CompanyEmployeeSkillset>();
            this.GNM_EmployeeBranch = new HashSet<GNM_EmployeeBranch>();
            this.GNM_EmployeeETODetails = new HashSet<GNM_EmployeeETODetails>();
            this.GNM_EmployeeETOLogDetails = new HashSet<GNM_EmployeeETOLogDetails>();
            this.GNM_EmployeedownLines = new HashSet<GNM_EmployeedownLines>();
            this.GNM_EmployeedownLines1 = new HashSet<GNM_EmployeedownLines>();
            this.GNM_CompanyEmployeeQUALIFICATION = new HashSet<GNM_CompanyEmployeeQUALIFICATION>();
            this.GNM_CompanyEmployeeEXPERIENCE = new HashSet<GNM_CompanyEmployeeEXPERIENCE>();
        }

        public int Company_Employee_ID { get; set; }
        public string Employee_ID { get; set; }
        public string Company_Employee_Name { get; set; }
        public int Company_ID { get; set; }
        public int Country_ID { get; set; }
        public int State_ID { get; set; }
        public string Company_Employee_MobileNumber { get; set; }
        public string Company_Employee_Landline_Number { get; set; }
        public string Company_Employee_ZipCode { get; set; }
        public Nullable<System.DateTime> Company_Employee_ActiveFrom { get; set; }
        public Nullable<System.DateTime> Company_Employee_ValidateUpTo { get; set; }
        public bool Company_Employee_Active { get; set; }
        public Nullable<int> Company_Employee_Manager_ID { get; set; }
        public string Company_Employee_Address { get; set; }
        public string Company_Employee_Location { get; set; }
        public string Company_Employee_Email { get; set; }
        public int Company_Employee_Department_ID { get; set; }
        public int Company_Employee_Designation_ID { get; set; }
        public int ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<decimal> HourlyRate { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public Nullable<bool> IsEligibleForOT { get; set; }
        public Nullable<byte> ExemptionHours { get; set; }
        public Nullable<bool> IsOnJob { get; set; }
        public Nullable<int> JobID { get; set; }
        public Nullable<bool> IsEligibleForIncentive { get; set; }
        public Nullable<bool> IsUnderProductiveMonitoring { get; set; }
        public Nullable<bool> IsConsideredForPayroll { get; set; }
        public Nullable<int> Bay_ID { get; set; }
        public string PHOTONAME { get; set; }
        public string EmployeeImagePath { get; set; }
        public Nullable<System.DateTime> ClockedOnJobStartTime { get; set; }

        public virtual GNM_Company GNM_Company { get; set; }
        public virtual ICollection<GNM_EmployeeTrainingDetails> GNM_EmployeeTrainingDetails { get; set; }
        public virtual ICollection<GNM_CompanyEmployeeLocale> GNM_CompanyEmployeeLocale { get; set; }
        public virtual ICollection<GNM_CompanyEmployeeSkillset> GNM_CompanyEmployeeSkillset { get; set; }
        public virtual ICollection<GNM_EmployeeBranch> GNM_EmployeeBranch { get; set; }
        public virtual ICollection<GNM_EmployeeETODetails> GNM_EmployeeETODetails { get; set; }
        public virtual ICollection<GNM_EmployeeETOLogDetails> GNM_EmployeeETOLogDetails { get; set; }
        public virtual ICollection<GNM_EmployeedownLines> GNM_EmployeedownLines { get; set; }
        public virtual ICollection<GNM_EmployeedownLines> GNM_EmployeedownLines1 { get; set; }
        public virtual ICollection<GNM_CompanyEmployeeQUALIFICATION> GNM_CompanyEmployeeQUALIFICATION { get; set; }
        public virtual ICollection<GNM_CompanyEmployeeEXPERIENCE> GNM_CompanyEmployeeEXPERIENCE { get; set; }
    }
    public partial class GNM_CompanyEmployeeEXPERIENCE
    {
        public int GNM_CompanyEmployeeWORKING_ID { get; set; }
        public int Company_Employee_ID { get; set; }
        public string COMPANY_NAME { get; set; }
        public Nullable<int> Department_ID { get; set; }
        public Nullable<int> DESIGNATION_ID { get; set; }
        public Nullable<int> EXPERIENCE { get; set; }

        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_CompanyEmployeeQUALIFICATION
    {
        public int EMPLOYEEQUALIFICATION_ID { get; set; }
        public int Company_Employee_ID { get; set; }
        public string QUALIFICATION { get; set; }
        public string COURSE { get; set; }
        public string INSTITUTENAME { get; set; }
        public Nullable<int> YOP { get; set; }
        public string PASSPERCENTAGE { get; set; }

        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_EmployeedownLines
    {
        public int EmployeeDownLine_ID { get; set; }
        public int CompanyEmployee_ID { get; set; }
        public int Branch_ID { get; set; }
        public int ManagerEmployee_ID { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
        public virtual GNM_CompanyEmployee GNM_CompanyEmployee1 { get; set; }
    }
    public partial class GNM_EmployeeETOLogDetails
    {
        public int EmployeeETOLog_ID { get; set; }
        public int Employee_ID { get; set; }
        public int ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<bool> IsAdded { get; set; }

        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }

    public partial class GNM_EmployeeETODetails
    {
        public int EmployeeETO_ID { get; set; }
        public int Employee_ID { get; set; }
        public Nullable<int> Year { get; set; }
        public Nullable<int> AvailableHours { get; set; }
        public Nullable<int> UsedHours { get; set; }
        public Nullable<int> PendingHours { get; set; }
        public Nullable<int> CarryoverHours { get; set; }
        public Nullable<int> UsedCarryoverHours { get; set; }
        public Nullable<int> BankCode_ID { get; set; }
        public Nullable<int> UsedCurrentYearETOHoursJanToMar { get; set; }
        public Nullable<System.DateTime> Validity { get; set; }
        public Nullable<bool> Isactive { get; set; }

        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_EmployeeBranch
    {
        public int EmployeeBranch_ID { get; set; }
        public int CompanyEmployee_ID { get; set; }
        public int Branch_ID { get; set; }
        public Nullable<bool> IsDefault { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }

    public partial class GNM_CompanyEmployeeSkillset
    {
        public int Employee_Skillset_ID { get; set; }
        public int CompnayEmployee_ID { get; set; }
        public int Skillset_ID { get; set; }
        public int Employee_Skillset_Rating { get; set; }
        public Nullable<int> Level_ID { get; set; }

        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }

    public partial class GNM_CompanyEmployeeLocale
    {
        public int Company_Employee_Locale_ID { get; set; }
        public int Company_Employee_ID { get; set; }
        public int Language_ID { get; set; }
        public string Company_Employee_Name { get; set; }
        public string Company_Employee_Address { get; set; }
        public string Company_Employee_Location { get; set; }

        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_EmployeeTrainingDetails
    {
        public GNM_EmployeeTrainingDetails()
        {
            this.GNM_EmployeeTrainingLocaleDetails = new HashSet<GNM_EmployeeTrainingLocaleDetails>();
        }

        public int TrainingDetails_ID { get; set; }
        public Nullable<int> Employee_ID { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public string TrainingName { get; set; }
        public Nullable<int> Level_ID { get; set; }
        public Nullable<System.DateTime> FromDate { get; set; }
        public Nullable<System.DateTime> ToDate { get; set; }
        public string Evaluation { get; set; }
        public string Faculty { get; set; }
        public Nullable<int> Status { get; set; }

        public virtual ICollection<GNM_EmployeeTrainingLocaleDetails> GNM_EmployeeTrainingLocaleDetails { get; set; }
        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_EmployeeTrainingLocaleDetails
    {
        public int TrainingLocaleDetails_ID { get; set; }
        public Nullable<int> Training_ID { get; set; }
        public string TrainingLocale_Name { get; set; }
        public string TrainingLocale_Evaluation { get; set; }
        public string TrainingLocale_Faculty { get; set; }
        public int Language_ID { get; set; }

        public virtual GNM_EmployeeTrainingDetails GNM_EmployeeTrainingDetails { get; set; }
    }

    public partial class GNM_HourlyRate
    {
        public int HourlyRate_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public decimal HourlyRate { get; set; }
        public System.DateTime EffectiveDate { get; set; }
        public Nullable<int> Modifiedby { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<decimal> OvertimeHourlyRate { get; set; }
        public Nullable<decimal> DoubleTimeHourlyRate { get; set; }
        public Nullable<decimal> BranchMobileRate { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
        public virtual GNM_Company GNM_Company { get; set; }
    }
    public partial class GNM_TERMSANDCONDITIONSLOCALE
    {
        public int TERMSANDCONDITIONSLOCALE_ID { get; set; }
        public int TERMSANDCONDITIONS_ID { get; set; }
        public string TERMSANDCONDITIONS { get; set; }

        public virtual GNM_TERMSANDCONDITIONS GNM_TERMSANDCONDITIONS { get; set; }
    }
    public partial class GNM_TERMSANDCONDITIONS
    {
        public GNM_TERMSANDCONDITIONS()
        {
            this.GNM_TERMSANDCONDITIONSLOCALE = new HashSet<GNM_TERMSANDCONDITIONSLOCALE>();
        }

        public int TERMSANDCONDITIONS_ID { get; set; }
        public Nullable<int> COMPANY_ID { get; set; }
        public Nullable<int> Branch_ID { get; set; }
        public Nullable<int> TERMS_ID { get; set; }
        public Nullable<int> OBJECT_ID { get; set; }
        public string TERMSANDCONDITIONS { get; set; }
        public bool ISDEFAULT { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
        public virtual GNM_Company GNM_Company { get; set; }
        public virtual ICollection<GNM_TERMSANDCONDITIONSLOCALE> GNM_TERMSANDCONDITIONSLOCALE { get; set; }
    }
    public partial class GNM_HEADERFOOTERPRINTLOCALE
    {
        public int HEADERFOOTERPRINTLOCALE_ID { get; set; }
        public int HEADERFOOTERPRINT_ID { get; set; }
        public int LANGUAGE_ID { get; set; }
        public string HEADERTEMPLATE { get; set; }
        public string FOOTERTEMPLATE { get; set; }

        public virtual GNM_HEADERFOOTERPRINT GNM_HEADERFOOTERPRINT { get; set; }
    }
    public partial class GNM_HEADERFOOTERPRINT
    {
        public GNM_HEADERFOOTERPRINT()
        {
            this.GNM_HEADERFOOTERPRINTLOCALE = new HashSet<GNM_HEADERFOOTERPRINTLOCALE>();
        }

        public int HEADERFOOTERPRINT_ID { get; set; }
        public Nullable<int> COMPANY_ID { get; set; }
        public string HEADERTEMPLATE { get; set; }
        public string FOOTERTEMPLATE { get; set; }
        public Nullable<int> Branch_ID { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
        public virtual GNM_Company GNM_Company { get; set; }
        public virtual ICollection<GNM_HEADERFOOTERPRINTLOCALE> GNM_HEADERFOOTERPRINTLOCALE { get; set; }
    }
    public partial class GNM_CompanyFinancialYear
    {
        public int Company_FinancialYear_ID { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public int Company_FinancialYear { get; set; }
        public System.DateTime Company_FinancialYear_FromDate { get; set; }
        public System.DateTime Company_FinancialYear_ToDate { get; set; }

        public virtual GNM_Company GNM_Company { get; set; }
    }
    public partial class GNM_CompanyLocale
    {
        public int Company_Locale_ID { get; set; }
        public int Company_ID { get; set; }
        public string Company_Name { get; set; }
        public string Company_ShortName { get; set; }
        public string Company_Address { get; set; }
        public int Language_ID { get; set; }

        public virtual GNM_Company GNM_Company { get; set; }
    }
    public partial class GNM_Company_Company_Relation
    {
        public int Company_Relationship_ID { get; set; }
        public int ManufacturerCompany_ID { get; set; }
        public int DealerCompany_ID { get; set; }

        public virtual GNM_Company GNM_Company { get; set; }
        public virtual GNM_Company GNM_Company1 { get; set; }
    }
    public partial class GNM_CompanyBrands
    {
        public int Company_Brand_ID { get; set; }
        public int Company_ID { get; set; }
        public int Brand_ID { get; set; }

        public virtual GNM_Company GNM_Company { get; set; }
    }
    public partial class GNM_ProductReading
    {
        public int ProductReadingID { get; set; }
        public Nullable<int> Product_ID { get; set; }
        public Nullable<int> ProductComponent_ID { get; set; }
        public int Mode { get; set; }
        public int Company_ID { get; set; }
        public string Reference_Number { get; set; }
        public System.DateTime Reference_Date { get; set; }
        public int Reading { get; set; }
        public Nullable<int> JobCardId { get; set; }
        public Nullable<decimal> AccumulatedHMR { get; set; }

        public virtual GNM_Product GNM_Product { get; set; }
        public virtual GNM_ProductComponent GNM_ProductComponent { get; set; }
    }
    public partial class GNM_ProductComponent
    {
        public GNM_ProductComponent()
        {
            this.GNM_ProductReading = new HashSet<GNM_ProductReading>();
        }

        public int ProductComponent_ID { get; set; }
        public int Product_ID { get; set; }
        public int Parts_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public string ProductComponent_SerialNumber { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> Reading { get; set; }
        public Nullable<bool> IsActive { get; set; }
        public string Remarks { get; set; }
        public Nullable<int> ExpectedLife { get; set; }
        public Nullable<int> ReManCycle { get; set; }
        public string UsageArea { get; set; }

        public virtual GNM_Product GNM_Product { get; set; }
        public virtual ICollection<GNM_ProductReading> GNM_ProductReading { get; set; }
    }

    public class CommonMethodForEmailandSMSList
    {
        public string TemplateCode { get; set; }
        public int CompanyId { get; set; }
        public string LanguageCode { get; set; }
        public int BranchId { get; set; }
        public string p1 { get; set; }
        public string p2 { get; set; }
        public string p3 { get; set; }
        public string p4 { get; set; }
        public string p5 { get; set; }
        public string p6 { get; set; }
        public string p7 { get; set; }
        public string p8 { get; set; }
        public string p9 { get; set; }
        public string p10 { get; set; }
        public string p11 { get; set; }
        public string p12 { get; set; }
        public string p13 { get; set; }
        public string p14 { get; set; }
        public string p15 { get; set; }
        public string p16 { get; set; }
        public string p17 { get; set; }
        public string p18 { get; set; }
        public string p19 { get; set; }
        public string p20 { get; set; }
    }

    public class WF_WFField
    {
        public int WFField_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public string WorkFlowFieldName { get; set; }

        public virtual ICollection<WF_WFFieldValue> GNM_WFFieldValue { get; set; }

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }

        public WF_WFField()
        {
            GNM_WFFieldValue = new HashSet<WF_WFFieldValue>();
        }
    }

    public partial class GNM_ProductStatusHistory
    {
        public int ProductServiceHistory_ID { get; set; }
        public int Product_ID { get; set; }
        public byte Mode { get; set; }
        public int Company_ID { get; set; }
        public System.DateTime ProductStatus_Date { get; set; }
        public string MobileNumber { get; set; }
        public int MachineStatus_ID { get; set; }

        public virtual GNM_Product GNM_Product { get; set; }
    }
    public partial class HD_UnregisteredServiceRequest
    {
        public int UnregisteredServiceRequest_ID { get; set; }
        public int Company_ID { get; set; }
        public string Product_Unique_Number { get; set; }
        public int Party_ID { get; set; }
        public string RequestDescription { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public string SerialNumber { get; set; }
        public string Email_ID { get; set; }
        public string Mobile { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public System.DateTime Date { get; set; }
        public Nullable<int> ServiceRequest_ID { get; set; }
        public string Remarks { get; set; } = string.Empty;
        public byte Status { get; set; }
        public Nullable<int> Locked_by_User_ID { get; set; }
        public Nullable<decimal> Coordinate_Latitude { get; set; }
        public Nullable<decimal> Coordinate_Longitude { get; set; }
        public string LatLongAddress { get; set; } = string.Empty;
    }
    public class WF_WFActionLocale
    {
        public int WFActionLocale_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public int WFAction_ID { get; set; }

        public int Language_ID { get; set; }

        public string WFAction_Name { get; set; } = string.Empty;

        public string ActionCode { get; set; } = string.Empty;

        public virtual WF_WFAction GNM_WFAction { get; set; } = null!;

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; } = null!;
    }

    public class WF_WFAction
    {
        public int WFAction_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public string WFAction_Name { get; set; } = string.Empty;

        public string ActionCode { get; set; } = string.Empty;

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; } = null!;

        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }

        public virtual ICollection<WF_WFActionLocale> GNM_WFActionLocale { get; set; }

        public WF_WFAction()
        {
            GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            GNM_WFActionLocale = new HashSet<WF_WFActionLocale>();
        }
    }

    public class WF_WFStepLink
    {
        public int WFStepLink_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public int Company_ID { get; set; }

        public int FrmWFSteps_ID { get; set; }

        public int WFAction_ID { get; set; }

        public int ToWFSteps_ID { get; set; }

        public int? Addresse_WFRole_ID { get; set; }

        public byte Addresse_Flag { get; set; }

        public bool IsSMSSentToCustomer { get; set; }

        public bool IsEmailSentToCustomer { get; set; }

        public bool IsSMSSentToAddressee { get; set; }

        public bool IsEmailSentToAddresse { get; set; }

        public bool AutoAllocationAllowed { get; set; }

        public bool IsVersionEnabled { get; set; }

        public int? InvokeParentWF_ID { get; set; }

        public int? InvokeParentWFLink_ID { get; set; }

        public int? InvokeChildObject_ID { get; set; }

        public int? InvokeChildObjectAction { get; set; }

        public int? WFField_ID { get; set; }

        public string AutoCondition { get; set; } = string.Empty;

        public virtual WF_WFAction GNM_WFAction { get; set; } = null!;

        public virtual WF_WFRole GNM_WFRole { get; set; } = null!;

        public virtual WF_WFSteps GNM_WFSteps { get; set; } = null!;

        public virtual WF_WFSteps GNM_WFSteps1 { get; set; } = null!;

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; } = null!;
    }
    public class MovementHistory
    {
        private int action_Chosen;

        private string action_Remarks;

        private string action_Time;

        private int actioned_By;

        private int addresse_ID;

        private DateTime received_Time;

        private int transaction_ID;

        private int wFCaseProgress_ID;

        private int wFSteps_ID;

        private int workFlow_ID;

        private string workFlow_Name;

        private string wFStep_Name;

        private string wFAction_Name;

        private string userorRoleName;

        private string assignTO;

        private byte addresse_Flag;

        public int Action_Chosen
        {
            get
            {
                return action_Chosen;
            }
            set
            {
                action_Chosen = value;
            }
        }

        public string Action_Remarks
        {
            get
            {
                return action_Remarks;
            }
            set
            {
                action_Remarks = value;
            }
        }

        public string Action_Time
        {
            get
            {
                return action_Time;
            }
            set
            {
                action_Time = value;
            }
        }

        public int Actioned_By
        {
            get
            {
                return actioned_By;
            }
            set
            {
                actioned_By = value;
            }
        }

        public int Addresse_ID
        {
            get
            {
                return addresse_ID;
            }
            set
            {
                addresse_ID = value;
            }
        }

        public DateTime Received_Time
        {
            get
            {
                return received_Time;
            }
            set
            {
                received_Time = value;
            }
        }

        public int Transaction_ID
        {
            get
            {
                return transaction_ID;
            }
            set
            {
                transaction_ID = value;
            }
        }

        public int WFCaseProgress_ID
        {
            get
            {
                return wFCaseProgress_ID;
            }
            set
            {
                wFCaseProgress_ID = value;
            }
        }

        public int WFSteps_ID
        {
            get
            {
                return wFSteps_ID;
            }
            set
            {
                wFSteps_ID = value;
            }
        }

        public int WorkFlow_ID
        {
            get
            {
                return workFlow_ID;
            }
            set
            {
                workFlow_ID = value;
            }
        }

        public string WorkFlow_Name
        {
            get
            {
                return workFlow_Name;
            }
            set
            {
                workFlow_Name = value;
            }
        }

        public string WFStep_Name
        {
            get
            {
                return wFStep_Name;
            }
            set
            {
                wFStep_Name = value;
            }
        }

        public string WFAction_Name
        {
            get
            {
                return wFAction_Name;
            }
            set
            {
                wFAction_Name = value;
            }
        }

        public string UserorRoleName
        {
            get
            {
                return userorRoleName;
            }
            set
            {
                userorRoleName = value;
            }
        }

        public string AssignTO
        {
            get
            {
                return assignTO;
            }
            set
            {
                assignTO = value;
            }
        }

        public byte Addresse_Flag
        {
            get
            {
                return addresse_Flag;
            }
            set
            {
                addresse_Flag = value;
            }
        }
    }
    public class GetScheduledDropinsList
    {
        public int Company_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
    }
    public class GetMovementofWorkFlowList
    {
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int Company_ID { get; set; }
        public int transactionNumber { get; set; }
    }
    public class SelectFieldSearchNameList
    {
        public int Company_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public string UserCulture { get; set; }
        public string value { get; set; }
        public bool IsCustomer { get; set; }
    }
    public class GetContactPersonDetailsList
    {
        public int ContactPersonID { get; set; }
    }
    public class SelectAllDropdownDataList
    {
        public int Company_ID { get; set; }
        public int IssueAreaId { get; set; }
        public int User_ID { get; set; }
        public int User_Employee_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
    }
    public class SRProductDetails
    {
        public int SRProductDetails_ID { get; set; }
        public int Model_ID { get; set; }
        public string Model_Name { get; set; }
        public int Brand_ID { get; set; }
        public string Brand_Name { get; set; }
        public int ProductType_ID { get; set; }
        public string ProductType_Name { get; set; }
        public decimal Quantity { get; set; }
        public decimal? WonQuantity { get; set; }//updated by kavitha for export to excel of product report
        public decimal ActiveQuantity { get; set; }
        public string LostSaleReasons { get; set; }
        public int? Competitor_ID { get; set; }
        public string Competitor_Name { get; set; }
        public int? CompetitorModel_ID { get; set; }
        public string CompetitorModel_Name { get; set; }
        public int? CompetitorBrand_ID { get; set; }
        public string CompetitorBrand_Name { get; set; }
        public int? CompetitorProductType_ID { get; set; }
        public string CompetitorProductType_Name { get; set; }
        public decimal? CompetitorPrice { get; set; }
        public int? PrimarySegment_ID { get; set; }
        public int? SecondarySegment_ID { get; set; }
        public string Remarks { get; set; }
        public int ServiceRequest_ID { get; set; }
        public Int64 RowNum { get; set; }
        public int? PACKINGTYPE_ID { get; set; }
        public string PackingType_Name { get; set; }
        public decimal Rate { get; set; }
        public string ReferenceDetails { get; set; }
    }
    public class SelHDProductDetailsList
    {
        public int SRIDGlobal { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public string UserCulture { get; set; }
    }
    public class GetMovementofWorkFlowforAllList
    {
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int Company_ID { get; set; }
        public int transactionNumber { get; set; }
    }
    public class MovementHistoryAll
    {
        public int? Action_Chosen { get; set; }
        public string Action_Remarks { get; set; }
        public string Action_TimeStr { get; set; }
        public DateTime? Action_Time { get; set; }
        public int? Actioned_By { get; set; }
        public byte Addresse_Flag { get; set; }
        public int? Addresse_ID { get; set; }
        public string AssignTO { get; set; }
        public DateTime Received_Time { get; set; }
        public int Transaction_ID { get; set; }
        public string UserorRoleName { get; set; }
        public string WFAction_Name { get; set; }
        public int WFCaseProgress_ID { get; set; }
        public string WFStep_Name { get; set; }
        public int WFSteps_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public string WorkFlow_Name { get; set; }
        public bool? Locked_Ind { get; set; }
        public int? WFNextStep_ID { get; set; }
        public string ServiceRequestNumber { get; set; }
    }

    public class WF_WFRole
    {
        public int WFRole_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public string WFRole_Name { get; set; } = string.Empty;

        public bool WfRole_IsAdmin { get; set; }

        public bool WfRole_AutoAllocationAllowed { get; set; }

        public bool? WFRole_IsRoleExternal { get; set; }

        public int? WFRole_ExternalCompany_ID { get; set; }

        public virtual ICollection<WF_WFRoleUser> GNM_WFRoleUser { get; set; }

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; } = null!;

        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }

        public virtual ICollection<WF_WFRoleLocale> GNM_WFRoleLocale { get; set; }

        public WF_WFRole()
        {
            GNM_WFRoleUser = new HashSet<WF_WFRoleUser>();
            GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            GNM_WFRoleLocale = new HashSet<WF_WFRoleLocale>();
        }
    }

    public class WF_WFRoleUser
    {
        public int WFRoleUser_ID { get; set; }

        public int WFRole_ID { get; set; }

        public int UserID { get; set; }

        public int ApprovalLimit { get; set; }

        public virtual WF_WFRole GNM_WFRole { get; set; } = null!;
    }

    public class WF_WFRoleLocale
    {
        public int WFRoleLocale_ID { get; set; }

        public int WFRole_ID { get; set; }

        public string WFRole_Name { get; set; } = string.Empty;

        public int Language_ID { get; set; }

        public virtual WF_WFRole GNM_WFRole { get; set; } = null!;
    }

    public class WF_WFSteps
    {
        public int WFSteps_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public string WFStep_Name { get; set; } = string.Empty;

        public int WFStepType_ID { get; set; }

        public int WFStepStatus_ID { get; set; }

        public bool WFStep_IsActive { get; set; }

        public string BranchCode { get; set; } = string.Empty;

        public virtual WF_WFStepType GNM_WFStepType { get; set; } = null!;

        public virtual WF_WFStepStatus GNM_WFStepStatus { get; set; } = null!;

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; } = null!;

        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }

        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink1 { get; set; }

        public virtual ICollection<WF_WFStepsLocale> GNM_WFStepsLocale { get; set; }

        public WF_WFSteps()
        {
            GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            GNM_WFStepLink1 = new HashSet<WF_WFStepLink>();
            GNM_WFStepsLocale = new HashSet<WF_WFStepsLocale>();
        }
    }
    public class WF_WFStepsLocale
    {
        public int WFStepsLocale_ID { get; set; }

        public int WFSteps_ID { get; set; }

        public string WFStep_Name { get; set; } = string.Empty;

        public int Language_ID { get; set; }

        public virtual WF_WFSteps GNM_WFSteps { get; set; } = null!;
    }
    public class WF_WFStepType
    {
        public int WFStepType_ID { get; set; }

        public string WFStepType_Nm { get; set; } = string.Empty;

        public virtual ICollection<WF_WFSteps> GNM_WFSteps { get; set; }

        public WF_WFStepType()
        {
            GNM_WFSteps = new HashSet<WF_WFSteps>();
        }
    }
    public class WF_WorkFlow
    {
        public int WorkFlow_ID { get; set; }

        public string WorkFlow_Name { get; set; } = string.Empty;

        public bool? AllQueue_Filter_IsBranch { get; set; }

        public virtual ICollection<WF_WFAction> GNM_WFAction { get; set; }

        public virtual ICollection<WF_WFField> GNM_WFField { get; set; }

        public virtual ICollection<WF_WFFieldValue> GNM_WFFieldValue { get; set; }

        public virtual ICollection<WF_WFRole> GNM_WFRole { get; set; }

        public virtual ICollection<WF_WFSteps> GNM_WFSteps { get; set; }

        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }

        public virtual ICollection<WF_WFActionLocale> GNM_WFActionLocale { get; set; }

        public WF_WorkFlow()
        {
            GNM_WFAction = new HashSet<WF_WFAction>();
            GNM_WFField = new HashSet<WF_WFField>();
            GNM_WFFieldValue = new HashSet<WF_WFFieldValue>();
            GNM_WFRole = new HashSet<WF_WFRole>();
            GNM_WFSteps = new HashSet<WF_WFSteps>();
            GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            GNM_WFActionLocale = new HashSet<WF_WFActionLocale>();
        }
    }

    public class WF_WFFieldValue
    {
        public int WFFieldValue_ID { get; set; }

        public int WFField_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public int Company_ID { get; set; }

        public int Transaction_ID { get; set; }

        public string WorkFlowFieldValue { get; set; } = string.Empty;

        public virtual WF_WFField GNM_WFField { get; set; } = null!;

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; } = null!;
    }

    public class SMSTemplate
    {
        public int Template_ID { get; set; }

        public string Param1 { get; set; } = string.Empty;

        public string Param2 { get; set; } = string.Empty;

        public string Param3 { get; set; } = string.Empty;

        public string Param4 { get; set; } = string.Empty;
    }

    public class Attachements
    {
        public int ATTACHMENTDETAIL_ID { get; set; }
        public string AttachmentIDS { get; set; } = string.Empty;
        public int TransactionID { get; set; }
        public string FILE_NAME { get; set; } = string.Empty;
        public string FILEDESCRIPTION { get; set; } = string.Empty;
        public string UPLOADBY { get; set; } = string.Empty;
        public DateTime UPLOADDATE { get; set; }
        public string UPLOADDATESORT { get; set; } = string.Empty;
        public string delete { get; set; } = string.Empty;
        public int Upload { get; set; }
        public string view { get; set; } = string.Empty;
        public int OBJECTID { get; set; }
        public string DocumentType { get; set; } = string.Empty;
        public int DocumentType_ID { get; set; }
        public int? DetailID { get; set; }
        public int ID { get; set; }
        public string edit { get; set; } = string.Empty;
        public string Tablename { get; set; } = string.Empty;
        public int PartID { get; set; }
        public string TransactionType { get; set; } = string.Empty;
        public string TransactionNumber { get; set; } = string.Empty;
        public string Date { get; set; } = string.Empty;
        public string Amount { get; set; } = string.Empty;
        public int ModelID { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Remarks { get; set; } = string.Empty;
    }

    // Request DTOs for Service Request endpoints
    public class InsertSRRequest
    {
        public string? ConnectionString { get; set; }
        public string Reopen { get; set; } = string.Empty;
        public string Data { get; set; } = string.Empty;
        public int BranchID { get; set; }
        public string UserLanguageCode { get; set; } = string.Empty;
        public List<string> RequestParams { get; set; } = new List<string>();
        public List<Attachements> HDAttachmentData { get; set; } = new List<Attachements>();
        public string Path { get; set; } = string.Empty;
        public string? ConnString { get; set; }
        public int LogException { get; set; } = 1;
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public string HelpDesk { get; set; } = string.Empty;
        public string HelpLineNumber { get; set; } = string.Empty;
        public int Language_ID { get; set; }
        public int Employee_ID { get; set; }
        public int MenuID { get; set; }
        public string HolidayDetails { get; set; } = string.Empty;
        public bool IsFromWebAPI { get; set; } = false;
    }

    public class InitialModeRequest
    {
        public string UserEmployeeId { get; set; } = string.Empty;
        public int CompanyId { get; set; }
        public string UserCulture { get; set; } = string.Empty;
        public string? ConnectionString { get; set; }
        public string? UnRegServiceRequestId { get; set; }
        public string? PartyId { get; set; }
        public string? ModelId { get; set; }
        public string? SerialNumber { get; set; }
        public string? RequestDesc { get; set; }
        public string? ModelName { get; set; }
        public string? Unique { get; set; }
        public string? ServiceRequestId { get; set; }
        public string? Reopen { get; set; }
        public int? Mode { get; set; }
        public int? StatusId { get; set; }
        public int? CompanyIdAlt { get; set; }
    }

    public class SelectSRDetailsRequest
    {
        public GNM_User User { get; set; } = new GNM_User();
        public int ServiceRequestID { get; set; }
        public int ChildTicketSequenceID { get; set; }
        public int BranchID { get; set; }
        public string? ConnectionString { get; set; }
        public bool IsReopen { get; set; }
        public bool IsOemDashboard { get; set; }
        public int? OemCompanyId { get; set; }
        public string UserCulture { get; set; } = string.Empty;
        public string GeneralLanguageCode { get; set; } = string.Empty;
        public string UserLanguageCode { get; set; } = string.Empty;
        public int MenuID { get; set; }
        public DateTime LoggedInDateTime { get; set; }
    }

    public class EditSRRequest
    {
        public string Data { get; set; } = string.Empty;
        public int BranchID { get; set; }
        public string UserLanguageCode { get; set; } = string.Empty;
        public List<string> RequestParams { get; set; } = new List<string>();
        public List<Attachements> HDAttachmentData { get; set; } = new List<Attachements>();
        public int LoginCompanyID { get; set; }
        public string? ConnString { get; set; }
        public int LogException { get; set; } = 1;
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public string HelpDesk { get; set; } = string.Empty;
        public string HelpLineNumber { get; set; } = string.Empty;
        public int Language_ID { get; set; }
        public int Employee_ID { get; set; }
        public int MenuID { get; set; }
        public string HolidayDetails { get; set; } = string.Empty;
        public bool IsFromWebAPI { get; set; } = false;
    }

    public class GetSRDetailsRequest
    {
        public GNM_User User { get; set; } = new GNM_User();
        public int ServiceRequestID { get; set; }
        public int ChildTicket_Sequence_ID { get; set; }
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public string? ConnString { get; set; }
        public bool IsReopen { get; set; }
        public bool IsOemDashboard { get; set; }
        public int? OemCompanyId { get; set; }
        public string UserCulture { get; set; } = string.Empty;
        public string GeneralLanguageCode { get; set; } = string.Empty;
        public string UserLanguageCode { get; set; } = string.Empty;
        public int MenuID { get; set; }
        public DateTime LoggedInDateTime { get; set; }
    }

    public class GetDetailsRequest
    {
        public DateTime? CallDate { get; set; }
        public int CompanyID { get; set; }
    }
}