using Microsoft.AspNetCore.Mvc;
using PBC.WorkflowService.Models;
using PBC.WorkflowService.Services;
using System.ComponentModel.DataAnnotations;

namespace PBC.WorkflowService.Controllers
{
    /// <summary>
    /// Controller for Workflow API operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class WorkflowAPIController : ControllerBase
    {
        private readonly IWorkflowAPIService _workflowAPIService;
        private readonly IWorkflowCommonService _workflowCommonService;
        private readonly ILogger<WorkflowAPIController> _logger;
        private readonly IConfiguration _configuration;

        public WorkflowAPIController(
            IWorkflowAPIService workflowAPIService,
            IWorkflowCommonService workflowCommonService,
            ILogger<WorkflowAPIController> logger,
            IConfiguration configuration)
        {
            _workflowAPIService = workflowAPIService;
            _workflowCommonService = workflowCommonService;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Insert workflow history for case progress tracking
        /// </summary>
        /// <param name="request">Workflow history insertion request</param>
        /// <returns>Success status</returns>
        /// <response code="200">Workflow history inserted successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("insert-workflow-history")]
        [ProducesResponseType(typeof(WorkflowAPIResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<WorkflowAPIResponse>> InsertWorkFlowHistory([FromBody] InsertWorkFlowHistoryRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/workflowapi/insert-workflow-history - Processing workflow history insertion");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = request.ConnectionString ?? _configuration.GetConnectionString("DefaultConnection");
                
                if (string.IsNullOrEmpty(connString))
                {
                    return BadRequest("Connection string is required");
                }

                _workflowAPIService.InsertWorkFlowHistory(
                    connString, 
                    request.CPDetails, 
                    request.SMSCustomerObj, 
                    request.SMSAssigneeObj, 
                    request.BranchID);

                return Ok(new WorkflowAPIResponse 
                { 
                    Success = true, 
                    Message = "Workflow history inserted successfully" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inserting workflow history");
                return StatusCode(500, new WorkflowAPIResponse 
                { 
                    Success = false, 
                    Message = "An error occurred while inserting workflow history" 
                });
            }
        }

        /// <summary>
        /// Check if invoke child object is configured for workflow step
        /// </summary>
        /// <param name="request">Child object check request</param>
        /// <returns>Boolean indicating if child object is configured</returns>
        /// <response code="200">Returns check result</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("check-invoke-child-object")]
        [ProducesResponseType(typeof(CheckInvokeChildObjectResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<CheckInvokeChildObjectResponse>> CheckIsInvokeChildObject([FromBody] CheckInvokeChildObjectRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/workflowapi/check-invoke-child-object - Checking child object configuration");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = _workflowAPIService.CheckIsInvokeChildObject(
                    request.CompanyID,
                    request.WorkFlowID,
                    request.StepID,
                    request.ActionID,
                    request.ToStepID);

                return Ok(new CheckInvokeChildObjectResponse 
                { 
                    Success = true, 
                    HasChildObject = result,
                    Message = "Child object check completed successfully" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking invoke child object");
                return StatusCode(500, new CheckInvokeChildObjectResponse 
                { 
                    Success = false, 
                    HasChildObject = false,
                    Message = "An error occurred while checking child object configuration" 
                });
            }
        }

        /// <summary>
        /// Get invoke child action details for workflow step link
        /// </summary>
        /// <param name="request">Child action request</param>
        /// <returns>Child action details</returns>
        /// <response code="200">Returns child action details</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("invoke-child-action")]
        [ProducesResponseType(typeof(InvokeChildActionResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<InvokeChildActionResponse>> InvokeChildAction([FromBody] InvokeChildActionRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/workflowapi/invoke-child-action - Getting child action details for step link ID: {StepLinkID}", request.StepLinkID);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = _workflowAPIService.InvokeChildAction(request.StepLinkID);

                return Ok(new InvokeChildActionResponse 
                { 
                    Success = true, 
                    ObjectName = result,
                    Message = "Child action details retrieved successfully" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting child action details");
                return StatusCode(500, new InvokeChildActionResponse 
                { 
                    Success = false, 
                    ObjectName = string.Empty,
                    Message = "An error occurred while getting child action details" 
                });
            }
        }

        /// <summary>
        /// Convert server time to local time based on branch timezone
        /// </summary>
        /// <param name="request">Local time conversion request</param>
        /// <returns>Converted local time</returns>
        /// <response code="200">Returns converted local time</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("local-time")]
        [ProducesResponseType(typeof(LocalTimeResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<LocalTimeResponse>> GetLocalTime([FromBody] LocalTimeRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/workflowapi/local-time - Converting time for branch ID: {BranchID}", request.BranchID);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = request.ConnectionString ?? _configuration.GetConnectionString("DefaultConnection");
                
                if (string.IsNullOrEmpty(connString))
                {
                    return BadRequest("Connection string is required");
                }

                var result = _workflowAPIService.LocalTime(connString, request.BranchID, request.ServerTime);

                return Ok(new LocalTimeResponse 
                { 
                    Success = true, 
                    LocalTime = result,
                    Message = "Local time conversion completed successfully" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting to local time");
                return StatusCode(500, new LocalTimeResponse
                {
                    Success = false,
                    LocalTime = DateTime.Now,
                    Message = "An error occurred while converting to local time"
                });
            }
        }

        /// <summary>
        /// Check if prefix/suffix configuration exists for the given parameters
        /// </summary>
        /// <param name="request">Prefix suffix check request</param>
        /// <returns>Boolean indicating if prefix/suffix configuration exists</returns>
        /// <response code="200">Returns check result</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("check-prefix-suffix")]
        [ProducesResponseType(typeof(CheckPrefixSuffixResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<CheckPrefixSuffixResponse>> CheckPrefixSuffix([FromBody] CheckPrefixSuffixRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/workflowapi/check-prefix-suffix - Checking prefix/suffix for Company: {CompanyID}, Branch: {BranchID}, Object: {ObjectName}",
                    request.CompanyID, request.BranchID, request.ObjectName);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _workflowCommonService.CheckPrefixSuffixAsync(
                    request.CompanyID,
                    request.BranchID,
                    request.ObjectName,
                    request.DbName,
                    request.ConnectionString);

                return Ok(new CheckPrefixSuffixResponse
                {
                    Success = true,
                    HasPrefixSuffix = result,
                    Message = "Prefix/suffix check completed successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking prefix/suffix for Company: {CompanyID}, Branch: {BranchID}, Object: {ObjectName}",
                    request.CompanyID, request.BranchID, request.ObjectName);
                return StatusCode(500, new CheckPrefixSuffixResponse
                {
                    Success = false,
                    HasPrefixSuffix = false,
                    Message = "An error occurred while checking prefix/suffix configuration"
                });
            }
        }

        /// <summary>
        /// Get object ID by object name
        /// </summary>
        /// <param name="request">Get object ID request</param>
        /// <returns>Object ID if found</returns>
        /// <response code="200">Returns object ID</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("get-object-id")]
        [ProducesResponseType(typeof(GetObjectIDResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<GetObjectIDResponse>> GetObjectID([FromBody] GetObjectIDRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/workflowapi/get-object-id - Getting object ID for Name: {Name}, DB: {DbName}",
                    request.Name, request.DbName);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _workflowCommonService.GetObjectIDAsync(
                    request.Name,
                    request.DbName,
                    request.ConnectionString);

                return Ok(new GetObjectIDResponse
                {
                    Success = true,
                    ObjectID = result,
                    Message = "Object ID retrieved successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object ID for Name: {Name}, DB: {DbName}",
                    request.Name, request.DbName);
                return StatusCode(500, new GetObjectIDResponse
                {
                    Success = false,
                    ObjectID = 0,
                    Message = "An error occurred while getting object ID"
                });
            }
        }
    }
}
